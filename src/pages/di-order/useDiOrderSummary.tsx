// libs
import { Table, TableProps } from 'antd';
import { useTranslation } from 'react-i18next';
import { useCallback } from 'react';
import Decimal from 'decimal.js';

// api
import { DiOrderOptions } from '@/api';

// components
import { Txt } from '@/components/TypographyMaster';

// utils
import { nTot } from '@/utils';

type DiOrderSummaryProps = {
  columns: TableProps<DiOrderOptions>['columns'];
};

const useDiOrderSummary = (props: DiOrderSummaryProps) => {
  // props
  const { columns } = props || {};

  // hooks
  const { t } = useTranslation('useDiOrderColumns');

  // compute
  const summary = useCallback(
    (pageData: readonly DiOrderOptions[]) => {
      const summaryData = pageData;

      // Calculate totals for crypto details with Decimal.js for precision
      const totalRequireAmount = summaryData.reduce((preValue, current) => {
        return new Decimal(preValue).plus(current.cryptoAmount).toNumber();
      }, 0);

      const totalFee = summaryData.reduce((preValue, current) => {
        return new Decimal(preValue).plus(current.totalFee).toNumber();
      }, 0);

      const totalTransferAmount = new Decimal(totalRequireAmount).minus(totalFee).toNumber();

      // Calculate total for fiat amount with Decimal.js for precision
      const totalFiatAmount = summaryData.reduce((preValue, current) => {
        return new Decimal(preValue).plus(current.fiatAmount).toNumber();
      }, 0);

      return (
        <Table.Summary fixed>
          <Table.Summary.Row>
            {columns?.map((mapC, index) => {
              const key = `${mapC.key}-${index}`;

              // Show subtotal label in the cryptoType column
              if (mapC.key === 'payerBankAccountName')
                return (
                  <Table.Summary.Cell
                    key={key}
                    index={index}
                    className='text-center font-bold'
                  >
                    <Txt>{t('subtotal')}</Txt>
                  </Table.Summary.Cell>
                );

              // Show fiat amount total in the fiatInfo column
              if (mapC.key === 'fiatInfo')
                return (
                  <Table.Summary.Cell
                    key={key}
                    index={index}
                    className='text-center'
                  >
                    <Txt type='secondary'>{nTot({ value: totalFiatAmount })}</Txt>
                  </Table.Summary.Cell>
                );

              // Show require amount total
              if (mapC.key === 'requireAmount')
                return (
                  <Table.Summary.Cell
                    key={key}
                    index={index}
                    className='text-center'
                  >
                    <Txt className='font-medium'>{nTot({ value: totalRequireAmount, digitsType: 'USDT' })}</Txt>
                  </Table.Summary.Cell>
                );

              // Show total fee total
              if (mapC.key === 'totalFee')
                return (
                  <Table.Summary.Cell
                    key={key}
                    index={index}
                    className='text-center'
                  >
                    <Txt className='font-medium'>{nTot({ value: totalFee, digitsType: 'USDT' })}</Txt>
                  </Table.Summary.Cell>
                );

              // Show transfer amount total
              if (mapC.key === 'transferAmount')
                return (
                  <Table.Summary.Cell
                    key={key}
                    index={index}
                    className='text-center'
                  >
                    <Txt className='font-medium'>{nTot({ value: totalTransferAmount, digitsType: 'USDT' })}</Txt>
                  </Table.Summary.Cell>
                );

              // Empty cells for other columns
              return (
                <Table.Summary.Cell
                  key={key}
                  index={index}
                />
              );
            })}
          </Table.Summary.Row>
        </Table.Summary>
      );
    },
    [columns, t],
  );

  return summary;
};

export default useDiOrderSummary;
