import { useMemo, useState } from 'react';
import dayjs from 'dayjs';
import { Avatar, Space, TableColumnsType } from 'antd';
import { useTranslation } from 'react-i18next';
import { DiOrderOptions, DiOrderListRes } from '@/api';
import usdt from '@/assets/usdt.png';
import { Txt, TxtCompressible } from '@/components/TypographyMaster';
import { TagDIOrderStatus, TagDiOrderType } from '@/components/TagAlpha';
import { cryptoEnumOptions, nTot } from '@/utils';
import { useThemeStore } from '@/store/useThemeStore';

const diUrlBase = decodeURIComponent(import.meta.env.VITE_DI_URL || 'https://k28.uxm-pay.uk/#/external/');
type DiOrderColumnsProps = {
  data: DiOrderListRes | undefined;
  setDiOrderMatchFrom: React.Dispatch<React.SetStateAction<DiOrderOptions | undefined>>;
};

const useDiOrderColumns = (useProps: DiOrderColumnsProps) => {
  // props
  const {} = useProps;

  // states
  const [hoveredKeys, setHoveredKeys] = useState<Array<string>>([]);
  const [expandedKeys, setExpandedKeys] = useState<Array<string>>([]);

  // hooks
  const { isWhite } = useThemeStore();
  const { t } = useTranslation('useDiOrderColumns');

  const columns = useMemo(() => {
    const result: TableColumnsType<DiOrderOptions> = [
      // 1. Transaction Type (mapped to Type)
      {
        title: <Txt>Type</Txt>,
        key: 'transactionType',
        align: 'center',
        render: ({ transactionType }) => {
          return <TagDiOrderType type={transactionType} />;
        },
      },
      // 2. Time
      {
        title: <Txt>{t('time')}</Txt>,
        key: 'time',
        align: 'center',
        render: (_, { createdAt, updatedAt }) => {
          const formattedDate = dayjs(createdAt);
          const formattedDateUpdated = dayjs(updatedAt);

          return (
            <main className='flex flex-col items-center'>
              <Space>
                <Txt type='secondary'>{t('createdAt')}:</Txt>
                <Txt>
                  {formattedDate.format('YYYY-MM-DD')} {formattedDate.format('HH:mm:ss')}
                </Txt>
              </Space>
              <Space>
                <Txt type='secondary'>{t('updatedAt')}:</Txt>
                <Txt>
                  {formattedDateUpdated.format('YYYY-MM-DD')} {formattedDateUpdated.format('HH:mm:ss')}
                </Txt>
              </Space>
            </main>
          );
        },
      },
      // 3. Order ID
      {
        key: 'orderUid',
        dataIndex: 'orderUid',
        title: <Txt>{t('orderUid')}</Txt>,
        align: 'center',
        render: (_, { orderUid }) => {
          const isExpanded = expandedKeys.includes(orderUid || '');
          const isHovered = hoveredKeys.includes(orderUid || '');
          return (
            <TxtCompressible
              {...{ isWhite, isHovered, isExpanded }}
              setIsHover={setHoveredKeys}
              setIsExpanded={setExpandedKeys}
              text={orderUid}
            />
          );
        },
      },
      // 4. Merchant Order ID
      {
        key: 'merchantOrderId',
        dataIndex: 'merchantOrderId',
        title: <Txt>{t('merchantOrderId')}</Txt>,
        align: 'center',
        render: (_, { merchantOrderId }) => {
          const isExpanded = expandedKeys.includes(merchantOrderId || '');
          const isHovered = hoveredKeys.includes(merchantOrderId || '');
          return (
            <TxtCompressible
              {...{ isWhite, isHovered, isExpanded }}
              setIsHover={setHoveredKeys}
              setIsExpanded={setExpandedKeys}
              text={merchantOrderId}
            />
          );
        },
      },
      // 5. Status
      {
        title: <Txt>{t('status')}</Txt>,
        key: 'status',
        align: 'center',
        render: (_, item) => {
          return <TagDIOrderStatus status={item.status} />;
        },
      },
      // 6. Redirect URL
      {
        key: 'redirectUrl',
        dataIndex: 'redirectUrl',
        title: <Txt>{t('redirectUrl')}</Txt>,
        align: 'center',
        render: (_, { entryCode }) => {
          const entryUrl = diUrlBase + entryCode;
          const isExpanded = expandedKeys.includes(entryUrl);
          const isHovered = hoveredKeys.includes(entryUrl);
          return (
            <TxtCompressible
              {...{ isWhite, isHovered, isExpanded }}
              setIsHover={setHoveredKeys}
              setIsExpanded={setExpandedKeys}
              text={entryUrl}
            />
          );
        },
      },
      // 7. Payer Bank Account Name
      {
        key: 'payerBankAccountName',
        dataIndex: 'payerBankAccountName',
        title: <Txt>{t('payerBankAccountName')}</Txt>,
        align: 'center',
        render: (_, { payerBankAccountName }) => <Txt>{payerBankAccountName}</Txt>,
      },
      // 8. Fiat Information
      {
        title: <Txt>{t('fiatInfo')}</Txt>,
        key: 'fiatInfo',
        align: 'center',
        render: (_, { fiatType, fiatAmount }) => (
          <main className='flex flex-col items-center'>
            <Txt>{fiatType}</Txt>
            <Txt type='secondary'>{nTot({ value: fiatAmount })}</Txt>
          </main>
        ),
      },
      // 9. Crypto Type
      {
        title: <Txt>{t('cryptoType')}</Txt>,
        key: 'cryptoType',
        align: 'center',
        render: (_, { cryptoType }) => {
          const cryptoOption = cryptoEnumOptions.find((option) => option.value === cryptoType);
          return (
            <main className='flex flex-col items-center'>
              <Space>
                <Avatar
                  size='small'
                  src={usdt}
                />
                <Txt>{cryptoOption?.label || cryptoType}</Txt>
              </Space>
            </main>
          );
        },
      },
      // 10. Require Amount
      {
        title: <Txt>{t('requireAmount')}</Txt>,
        key: 'requireAmount',
        align: 'center',
        render: (_, { cryptoAmount }) => (
          <Txt>{nTot({ value: cryptoAmount, digitsType: 'USDT' })}</Txt>
        ),
      },
      // 11. Total Fee
      {
        title: <Txt>{t('totalFee')}</Txt>,
        key: 'totalFee',
        align: 'center',
        render: (_, { totalFee }) => (
          <Txt>{nTot({ value: totalFee, digitsType: 'USDT' })}</Txt>
        ),
      },
      // 12. Transfer Amount
      {
        title: <Txt>{t('transferAmount')}</Txt>,
        key: 'transferAmount',
        align: 'center',
        render: (_, { cryptoAmount, totalFee }) => {
          const transferAmount = cryptoAmount - totalFee;
          return (
            <Txt>{nTot({ value: transferAmount, digitsType: 'USDT' })}</Txt>
          );
        },
      },
      // 11. Merchant Notification Status
      {
        title: <Txt>{t('merchantNotified')}</Txt>,
        key: 'isMerchantNotified',
        align: 'center',
        render: (_, { isMerchantNotified }) => (
          <Txt type={isMerchantNotified ? 'success' : 'danger'}>
            {isMerchantNotified ? t('notified') : t('notNotified')}
          </Txt>
        ),
      },
    ];

    return result;
  }, [expandedKeys, hoveredKeys, isWhite, t]);

  return { columns };
};

export default useDiOrderColumns;
